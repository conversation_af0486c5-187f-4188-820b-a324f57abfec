<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Access Test</title>
</head>
<body>
    <h1>PDF Access Test</h1>
    <div id="results"></div>
    <button onclick="testPdfAccess()">Test PDF Access</button>

    <script>
        async function testPdfAccess() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing PDF access...</p>';
            
            const paths = [
                './PDF/Birth Template.pdf',
                'PDF/Birth Template.pdf',
                './PDF/Birth%20Template.pdf',
                'PDF/Birth%20Template.pdf',
                encodeURI('./PDF/Birth Template.pdf'),
                encodeURI('PDF/Birth Template.pdf')
            ];
            
            for (const path of paths) {
                try {
                    console.log(`Testing path: ${path}`);
                    const response = await fetch(path);
                    resultsDiv.innerHTML += `<p>Path: ${path} - Status: ${response.status} ${response.statusText}</p>`;
                    
                    if (response.ok) {
                        const arrayBuffer = await response.arrayBuffer();
                        resultsDiv.innerHTML += `<p style="color: green;">✓ SUCCESS: Loaded ${arrayBuffer.byteLength} bytes from ${path}</p>`;
                        return;
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `<p style="color: red;">✗ ERROR for ${path}: ${error.message}</p>`;
                }
            }
            
            resultsDiv.innerHTML += '<p style="color: red; font-weight: bold;">All paths failed!</p>';
            resultsDiv.innerHTML += '<p><strong>Troubleshooting:</strong></p>';
            resultsDiv.innerHTML += '<ul>';
            resultsDiv.innerHTML += '<li>Make sure you\'re running this from a web server (not file:// protocol)</li>';
            resultsDiv.innerHTML += '<li>Check that PDF/Birth Template.pdf exists in the same directory</li>';
            resultsDiv.innerHTML += '<li>Try renaming the PDF file to remove spaces (e.g., "BirthTemplate.pdf")</li>';
            resultsDiv.innerHTML += '</ul>';
        }
        
        // Auto-run test on page load
        window.addEventListener('load', testPdfAccess);
    </script>
</body>
</html>
