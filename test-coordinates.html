<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Coordinates</title>
    <script src="https://unpkg.com/pdf-lib/dist/pdf-lib.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0; }
        button:hover { background: #0056b3; }
        #status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Test PDF Coordinates</h1>
    <p>This will generate a test PDF with sample text at all coordinate positions to verify alignment.</p>
    
    <button onclick="generateTestPdf()">Generate Test PDF</button>
    <div id="status"></div>

    <script>
        // Updated coordinates based on PDF analysis
        const PDF_COORDINATES = {
            name: { x: 80, y: 500 },
            sex: { x: 480, y: 500 },
            dateOfBirth: { x: 80, y: 535 },
            dateOfBirthWords: { x: 80, y: 550 },
            placeOfBirth: { x: 480, y: 570 },
            motherName: { x: 80, y: 650 },
            fatherName: { x: 480, y: 650 },
            motherAadhaar: { x: 80, y: 713 },
            fatherAadhaar: { x: 480, y: 713 },
            addressAtBirth: { x: 80, y: 760 },
            permanentAddress: { x: 80, y: 800 },
            registrationNumber: { x: 480, y: 468 },
            dateOfRegistration: { x: 150, y: 850 },
            dateOfIssue: { x: 350, y: 850 },
            updatedOn: { x: 150, y: 870 }
        };

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = type;
            statusDiv.style.display = 'block';
        }

        async function generateTestPdf() {
            try {
                showStatus('Loading PDF template...', 'info');
                
                const response = await fetch('PDF/Birth Template.pdf');
                if (!response.ok) {
                    throw new Error('Could not load PDF template');
                }
                
                const existingPdfBytes = await response.arrayBuffer();
                const pdfDoc = await PDFLib.PDFDocument.load(existingPdfBytes);
                
                const pages = pdfDoc.getPages();
                const firstPage = pages[0];
                const { height } = firstPage.getSize();
                
                // Test data - using only ASCII characters to avoid encoding issues
                const testData = {
                    name: 'JOHN DOE',
                    sex: 'Male',
                    dateOfBirth: '12-08-2025',
                    dateOfBirthWords: 'Twelfth August Two Thousand Twenty-Five',
                    placeOfBirth: 'HOME',
                    motherName: 'JANE DOE',
                    fatherName: 'ROBERT DOE',
                    motherAadhaar: '1234-5678-9012',
                    fatherAadhaar: '**************',
                    addressAtBirth: 'Test Address Line 1',
                    permanentAddress: 'Permanent Address Line 1',
                    registrationNumber: '148621456321456821',
                    dateOfRegistration: '12-08-2025',
                    dateOfIssue: '12-08-2025',
                    updatedOn: '12-08-2025 10:30:45'
                };
                
                // Add test text at each coordinate
                Object.keys(PDF_COORDINATES).forEach(key => {
                    const coords = PDF_COORDINATES[key];
                    let text = testData[key] || `TEST_${key.toUpperCase()}`;

                    try {
                        // Clean text to ensure it only contains ASCII characters
                        text = text.replace(/[^\x00-\x7F]/g, "?"); // Replace non-ASCII with ?

                        if (key === 'addressAtBirth' || key === 'permanentAddress') {
                            // Single line for testing
                            firstPage.drawText(text, {
                                x: coords.x,
                                y: height - coords.y,
                                size: 10,
                                color: PDFLib.rgb(1, 0, 0) // Red for visibility
                            });
                        } else {
                            firstPage.drawText(text, {
                                x: coords.x,
                                y: height - coords.y,
                                size: 11,
                                color: PDFLib.rgb(1, 0, 0) // Red for visibility
                            });
                        }
                    } catch (error) {
                        console.error(`Error drawing text for ${key}:`, error);
                        // Draw a simple marker instead
                        firstPage.drawText(`[${key}]`, {
                            x: coords.x,
                            y: height - coords.y,
                            size: 10,
                            color: PDFLib.rgb(1, 0, 0)
                        });
                    }
                });
                
                // Generate and download
                const pdfBytes = await pdfDoc.save();
                const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                const url = URL.createObjectURL(blob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = 'test_coordinates.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                URL.revokeObjectURL(url);
                
                showStatus('Test PDF generated! Check the positioning and adjust coordinates in script.js if needed.', 'success');
                
            } catch (error) {
                console.error('Error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
