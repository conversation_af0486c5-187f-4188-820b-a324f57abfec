<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Coordinate Tester</title>
    <script src="https://unpkg.com/pdf-lib/dist/pdf-lib.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 5px;
            margin-right: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>PDF Coordinate Tester</h1>
    <p>Use this tool to find the exact coordinates for placing text in your PDF template.</p>
    
    <div class="controls">
        <div class="control-group">
            <label>Field:</label>
            <select id="fieldSelect">
                <option value="name">Name</option>
                <option value="sex">Sex</option>
                <option value="dateOfBirth">Date of Birth</option>
                <option value="dateOfBirthWords">Date of Birth (Words)</option>
                <option value="motherName">Mother's Name</option>
                <option value="fatherName">Father's Name</option>
                <option value="addressAtBirth">Address at Birth</option>
                <option value="permanentAddress">Permanent Address</option>
                <option value="registrationNumber">Registration Number</option>
                <option value="dateOfRegistration">Date of Registration</option>
                <option value="dateOfIssue">Date of Issue</option>
                <option value="updatedOn">Updated On</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>X Coordinate:</label>
            <input type="number" id="xCoord" value="150" min="0" max="600">
            
            <label>Y Coordinate:</label>
            <input type="number" id="yCoord" value="200" min="0" max="800">
        </div>
        
        <div class="control-group">
            <label>Test Text:</label>
            <input type="text" id="testText" value="Sample Text" style="width: 200px;">
            
            <label>Font Size:</label>
            <input type="number" id="fontSize" value="12" min="8" max="20">
        </div>
        
        <button onclick="testCoordinates()">Test Coordinates</button>
        <button onclick="loadCurrentCoordinates()">Load Current Coordinates</button>
        <button onclick="generateCoordinateCode()">Generate Code</button>
    </div>
    
    <div id="status"></div>
    
    <div id="coordinateCode" style="display: none; margin-top: 20px;">
        <h3>Generated Coordinate Code:</h3>
        <pre id="codeOutput" style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;"></pre>
    </div>

    <script>
        // Current coordinates from your script.js
        const CURRENT_COORDINATES = {
            name: { x: 150, y: 200 },
            sex: { x: 400, y: 200 },
            dateOfBirth: { x: 150, y: 240 },
            dateOfBirthWords: { x: 150, y: 260 },
            motherName: { x: 150, y: 320 },
            fatherName: { x: 150, y: 380 },
            addressAtBirth: { x: 150, y: 480 },
            permanentAddress: { x: 150, y: 560 },
            registrationNumber: { x: 400, y: 650 },
            dateOfRegistration: { x: 150, y: 700 },
            dateOfIssue: { x: 400, y: 700 },
            updatedOn: { x: 150, y: 750 }
        };

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = type;
            statusDiv.style.display = 'block';
        }

        function loadCurrentCoordinates() {
            const field = document.getElementById('fieldSelect').value;
            const coords = CURRENT_COORDINATES[field];
            if (coords) {
                document.getElementById('xCoord').value = coords.x;
                document.getElementById('yCoord').value = coords.y;
                showStatus(`Loaded coordinates for ${field}: (${coords.x}, ${coords.y})`, 'info');
            }
        }

        async function testCoordinates() {
            try {
                showStatus('Loading PDF template...', 'info');
                
                // Load PDF template
                const response = await fetch('PDF/Birth Template.pdf');
                if (!response.ok) {
                    throw new Error('Could not load PDF template');
                }
                
                const existingPdfBytes = await response.arrayBuffer();
                const pdfDoc = await PDFLib.PDFDocument.load(existingPdfBytes);
                
                // Get coordinates and text
                const x = parseInt(document.getElementById('xCoord').value);
                const y = parseInt(document.getElementById('yCoord').value);
                const text = document.getElementById('testText').value;
                const fontSize = parseInt(document.getElementById('fontSize').value);
                
                // Add test text
                const pages = pdfDoc.getPages();
                const firstPage = pages[0];
                const { height } = firstPage.getSize();
                
                firstPage.drawText(text, {
                    x: x,
                    y: height - y,
                    size: fontSize,
                    color: PDFLib.rgb(1, 0, 0) // Red color for visibility
                });
                
                // Generate and download test PDF
                const pdfBytes = await pdfDoc.save();
                const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                const url = URL.createObjectURL(blob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `test_coordinates_${x}_${y}.pdf`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                URL.revokeObjectURL(url);
                
                showStatus(`Test PDF generated with text "${text}" at coordinates (${x}, ${y})`, 'success');
                
            } catch (error) {
                console.error('Error:', error);
                showStatus(`Error: ${error.message}`, 'error');
            }
        }

        function generateCoordinateCode() {
            const field = document.getElementById('fieldSelect').value;
            const x = parseInt(document.getElementById('xCoord').value);
            const y = parseInt(document.getElementById('yCoord').value);
            
            // Update the coordinates object
            CURRENT_COORDINATES[field] = { x, y };
            
            // Generate the code
            let code = 'const PDF_COORDINATES = {\n';
            for (const [key, coords] of Object.entries(CURRENT_COORDINATES)) {
                code += `    ${key}: { x: ${coords.x}, y: ${coords.y} },\n`;
            }
            code += '};';
            
            document.getElementById('codeOutput').textContent = code;
            document.getElementById('coordinateCode').style.display = 'block';
            
            showStatus(`Updated coordinates for ${field} to (${x}, ${y})`, 'success');
        }

        // Load initial coordinates when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentCoordinates();
        });

        // Auto-load coordinates when field selection changes
        document.getElementById('fieldSelect').addEventListener('change', loadCurrentCoordinates);
    </script>
</body>
</html>
