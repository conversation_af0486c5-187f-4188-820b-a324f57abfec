<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Precise Birth Certificate Generator</title>
    <script src="https://unpkg.com/pdf-lib/dist/pdf-lib.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .form-container {
            padding: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .coordinates-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }

        .coordinates-info h3 {
            color: #495057;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Precise Birth Certificate Generator</h1>
            <p>Fill out the form below to generate your birth certificate with exact positioning</p>
        </div>
        
        <div class="form-container">
            <div class="coordinates-info">
                <h3>📍 Positioning Information</h3>
                <p>This system uses precise coordinates to position text exactly where it should appear in your PDF template. 
                If text appears in the wrong position, you can adjust the coordinates in the JavaScript section.</p>
            </div>
            
            <form id="birthForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" id="name" name="name" required placeholder="Enter full name">
                    </div>
                    
                    <div class="form-group">
                        <label for="sex">Sex *</label>
                        <select id="sex" name="sex" required>
                            <option value="">Select Sex</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth *</label>
                        <input type="date" id="dateOfBirth" name="dateOfBirth" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="motherName">Name of Mother *</label>
                        <input type="text" id="motherName" name="motherName" required placeholder="Enter mother's name">
                    </div>
                    
                    <div class="form-group">
                        <label for="fatherName">Name of Father *</label>
                        <input type="text" id="fatherName" name="fatherName" required placeholder="Enter father's name">
                    </div>
                    
                    <div class="form-group">
                        <label for="addressAtBirth">Address at Time of Birth *</label>
                        <input type="text" id="addressAtBirth" name="addressAtBirth" required placeholder="Address when child was born">
                    </div>
                    
                    <div class="form-group">
                        <label for="permanentAddress">Permanent Address of Parents *</label>
                        <input type="text" id="permanentAddress" name="permanentAddress" required placeholder="Current permanent address">
                    </div>
                    
                    <div class="form-group">
                        <label for="registrationNumber">Registration Number</label>
                        <input type="text" id="registrationNumber" name="registrationNumber" placeholder="Birth registration number">
                    </div>
                </div>
                
                <button type="submit" class="generate-btn" id="generateBtn">
                    Generate Precise PDF Certificate
                </button>
                
                <div id="status" class="status"></div>
            </form>
        </div>
    </div>

    <script>
        // PRECISE COORDINATES FOR PDF POSITIONING
        // Adjust these coordinates based on your PDF template layout
        // Coordinates are from bottom-left corner of PDF (PDF coordinate system)
        const PDF_COORDINATES = {
            // Name - appears next to "Name" label
            name: { x: 200, y: 700, size: 12 },
            
            // Sex - appears next to "Sex" label  
            sex: { x: 200, y: 670, size: 12 },
            
            // Date of Birth - numeric format below "Date of Birth" label
            dateOfBirthNumeric: { x: 150, y: 640, size: 12 },
            
            // Date of Birth - in words, one line below numeric
            dateOfBirthWords: { x: 150, y: 625, size: 11 },
            
            // Mother's Name - below "Name of Mother" label
            motherName: { x: 150, y: 580, size: 12 },
            
            // Father's Name - below "Name of Father" label
            fatherName: { x: 150, y: 540, size: 12 },
            
            // Address at Birth - below "Address of Parents at the time of Birth of Child"
            addressAtBirth: { x: 150, y: 480, size: 11 },
            
            // Permanent Address - below "Permanent Address of Parents"
            permanentAddress: { x: 150, y: 440, size: 11 },
            
            // Registration Number - in corresponding position
            registrationNumber: { x: 200, y: 400, size: 12 },
            
            // Date of Registration - auto-filled with current date
            dateOfRegistration: { x: 200, y: 360, size: 12 },
            
            // Date of Issue - auto-filled with current date
            dateOfIssue: { x: 200, y: 320, size: 12 },
            
            // Updated On - auto-filled with current date and time
            updatedOn: { x: 200, y: 280, size: 11 }
        };

        // Function to convert date to words
        function dateToWords(dateString) {
            const date = new Date(dateString);
            const day = date.getDate();
            const month = date.getMonth();
            const year = date.getFullYear();
            
            const dayWords = [
                '', 'First', 'Second', 'Third', 'Fourth', 'Fifth', 'Sixth', 'Seventh', 'Eighth', 'Ninth', 'Tenth',
                'Eleventh', 'Twelfth', 'Thirteenth', 'Fourteenth', 'Fifteenth', 'Sixteenth', 'Seventeenth', 
                'Eighteenth', 'Nineteenth', 'Twentieth', 'Twenty-First', 'Twenty-Second', 'Twenty-Third', 
                'Twenty-Fourth', 'Twenty-Fifth', 'Twenty-Sixth', 'Twenty-Seventh', 'Twenty-Eighth', 
                'Twenty-Ninth', 'Thirtieth', 'Thirty-First'
            ];
            
            const monthWords = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            
            // Convert year to words
            function yearToWords(year) {
                const thousands = Math.floor(year / 1000);
                const hundreds = Math.floor((year % 1000) / 100);
                const tens = Math.floor((year % 100) / 10);
                const ones = year % 10;
                
                let result = '';
                
                if (thousands === 2) result += 'Two Thousand ';
                else if (thousands === 1) result += 'One Thousand ';
                
                if (hundreds > 0) {
                    const hundredWords = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
                    result += hundredWords[hundreds] + ' Hundred ';
                }
                
                const tensOnes = tens * 10 + ones;
                if (tensOnes >= 20) {
                    const tensWords = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
                    result += tensWords[tens];
                    if (ones > 0) {
                        const onesWords = ['', ' One', ' Two', ' Three', ' Four', ' Five', ' Six', ' Seven', ' Eight', ' Nine'];
                        result += onesWords[ones];
                    }
                } else if (tensOnes >= 10) {
                    const teenWords = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
                    result += teenWords[tensOnes - 10];
                } else if (tensOnes > 0) {
                    const onesWords = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
                    result += onesWords[tensOnes];
                }
                
                return result.trim();
            }
            
            return `${dayWords[day]} ${monthWords[month]} ${yearToWords(year)}`;
        }

        // Function to format current date
        function getCurrentDate() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            return `${day}-${month}-${year}`;
        }

        // Function to format current date and time
        function getCurrentDateTime() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function setButtonLoading(loading) {
            const btn = document.getElementById('generateBtn');
            btn.disabled = loading;
            btn.textContent = loading ? 'Generating Precise PDF...' : 'Generate Precise PDF Certificate';
        }

        async function loadPdfTemplate() {
            const possiblePaths = [
                'PDF/Birth Template final.pdf',
                './PDF/Birth Template final.pdf',
                'PDF/Birth%20Template%20final.pdf',
                './PDF/Birth%20Template%20final.pdf',
                'PDF/Birth Template.pdf',
                './PDF/Birth Template.pdf',
                'PDF/BirthTemplate.pdf',
                './PDF/BirthTemplate.pdf'
            ];
            
            for (const path of possiblePaths) {
                try {
                    console.log(`Trying to load PDF from: ${path}`);
                    const response = await fetch(path);
                    if (response.ok) {
                        const arrayBuffer = await response.arrayBuffer();
                        console.log(`Successfully loaded PDF from: ${path} (${arrayBuffer.byteLength} bytes)`);
                        return arrayBuffer;
                    }
                } catch (error) {
                    console.log(`Error trying path ${path}:`, error);
                    continue;
                }
            }
            
            throw new Error('Could not load PDF template. Please ensure "Birth Template final.pdf" is in the PDF folder and accessible via web server.');
        }
