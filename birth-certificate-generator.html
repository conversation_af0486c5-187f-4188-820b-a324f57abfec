<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birth Certificate Generator</title>
    <script src="https://unpkg.com/pdf-lib/dist/pdf-lib.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Birth Certificate Generator</h1>
            <p>Fill out the form below to generate your birth certificate PDF</p>
        </div>
        
        <div class="form-container">
            <form id="birthForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="sex">Sex *</label>
                        <select id="sex" name="sex" required>
                            <option value="">Select Sex</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth *</label>
                        <input type="date" id="dateOfBirth" name="dateOfBirth" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="placeOfBirth">Place of Birth</label>
                        <input type="text" id="placeOfBirth" name="placeOfBirth">
                    </div>
                    
                    <div class="form-group">
                        <label for="fatherName">Father's Name</label>
                        <input type="text" id="fatherName" name="fatherName">
                    </div>
                    
                    <div class="form-group">
                        <label for="motherName">Mother's Name</label>
                        <input type="text" id="motherName" name="motherName">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Address</label>
                        <input type="text" id="address" name="address">
                    </div>
                    
                    <div class="form-group">
                        <label for="registrationNumber">Registration Number</label>
                        <input type="text" id="registrationNumber" name="registrationNumber">
                    </div>
                </div>
                
                <button type="submit" class="generate-btn" id="generateBtn">
                    Generate PDF Certificate
                </button>
                
                <div id="status" class="status"></div>
            </form>
        </div>
    </div>

    <script>
        // PDF field coordinates for fallback method (adjust as needed)
        const PDF_COORDINATES = {
            name: { x: 200, y: 700 },
            sex: { x: 200, y: 650 },
            dateOfBirth: { x: 200, y: 600 },
            placeOfBirth: { x: 200, y: 550 },
            fatherName: { x: 200, y: 500 },
            motherName: { x: 200, y: 450 },
            address: { x: 200, y: 400 },
            registrationNumber: { x: 200, y: 350 }
        };

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function setButtonLoading(loading) {
            const btn = document.getElementById('generateBtn');
            btn.disabled = loading;
            btn.textContent = loading ? 'Generating PDF...' : 'Generate PDF Certificate';
        }

        async function loadPdfTemplate() {
            try {
                const response = await fetch('./PDF/Birth Template.pdf');
                if (!response.ok) {
                    throw new Error(`Failed to load PDF template: ${response.status} ${response.statusText}`);
                }
                return await response.arrayBuffer();
            } catch (error) {
                console.error('Error loading PDF template:', error);
                throw new Error('Could not load PDF template. Please ensure "Birth Template.pdf" is in the PDF folder.');
            }
        }

        async function fillPdfWithAcroForms(pdfDoc, formData) {
            const form = pdfDoc.getForm();
            const fields = form.getFields();
            
            // Log all available field names for debugging
            console.log('Available PDF form fields:', fields.map(field => field.getName()));
            
            // Common field name mappings (adjust based on your PDF's actual field names)
            const fieldMappings = {
                'name': ['NAME', 'Name', 'FullName', 'FULL_NAME', 'name'],
                'sex': ['SEX', 'Sex', 'Gender', 'GENDER', 'sex'],
                'dateOfBirth': ['DOB', 'DateOfBirth', 'DATE_OF_BIRTH', 'BirthDate', 'dateOfBirth'],
                'placeOfBirth': ['POB', 'PlaceOfBirth', 'PLACE_OF_BIRTH', 'BirthPlace', 'placeOfBirth'],
                'fatherName': ['FATHER', 'FatherName', 'FATHER_NAME', 'Father', 'fatherName'],
                'motherName': ['MOTHER', 'MotherName', 'MOTHER_NAME', 'Mother', 'motherName'],
                'address': ['ADDRESS', 'Address', 'ADDR', 'address'],
                'registrationNumber': ['REG_NO', 'RegistrationNumber', 'REGISTRATION_NUMBER', 'RegNo', 'registrationNumber']
            };
            
            let fieldsFound = 0;
            
            // Try to fill each field using possible field name variations
            Object.keys(fieldMappings).forEach(formKey => {
                const value = formData[formKey];
                if (value) {
                    const possibleNames = fieldMappings[formKey];
                    
                    for (const fieldName of possibleNames) {
                        try {
                            const field = form.getTextField(fieldName);
                            if (field) {
                                field.setText(value);
                                console.log(`Successfully filled field "${fieldName}" with "${value}"`);
                                fieldsFound++;
                                break;
                            }
                        } catch (error) {
                            // Field doesn't exist, try next name
                            continue;
                        }
                    }
                }
            });
            
            console.log(`Successfully filled ${fieldsFound} form fields`);
            return fieldsFound > 0;
        }

        async function fillPdfWithCoordinates(pdfDoc, formData) {
            const pages = pdfDoc.getPages();
            const firstPage = pages[0];
            const { height } = firstPage.getSize();
            
            console.log('Using coordinate-based text placement as fallback');
            
            // Add text at specified coordinates
            Object.keys(PDF_COORDINATES).forEach(key => {
                const value = formData[key];
                if (value) {
                    const coords = PDF_COORDINATES[key];
                    firstPage.drawText(value, {
                        x: coords.x,
                        y: height - coords.y, // PDF coordinates are from bottom-left
                        size: 12,
                        color: PDFLib.rgb(0, 0, 0)
                    });
                    console.log(`Added text "${value}" at coordinates (${coords.x}, ${coords.y})`);
                }
            });
        }

        async function generatePdf(formData) {
            try {
                setButtonLoading(true);
                showStatus('Loading PDF template...', 'info');
                
                // Load the PDF template
                const existingPdfBytes = await loadPdfTemplate();
                
                showStatus('Processing PDF...', 'info');
                
                // Load PDF with pdf-lib
                const pdfDoc = await PDFLib.PDFDocument.load(existingPdfBytes);
                
                // Try to fill using AcroForm fields first
                let useAcroForms = false;
                try {
                    const form = pdfDoc.getForm();
                    useAcroForms = await fillPdfWithAcroForms(pdfDoc, formData);
                    
                    if (useAcroForms) {
                        // Flatten the form to make fields non-editable
                        form.flatten();
                        showStatus('Filled PDF using form fields...', 'info');
                    }
                } catch (error) {
                    console.log('No AcroForm fields found or error accessing them:', error);
                    useAcroForms = false;
                }
                
                // If no AcroForm fields were found or filled, use coordinate-based approach
                if (!useAcroForms) {
                    await fillPdfWithCoordinates(pdfDoc, formData);
                    showStatus('Filled PDF using coordinate positioning...', 'info');
                }
                
                // Generate the PDF
                showStatus('Generating final PDF...', 'info');
                const pdfBytes = await pdfDoc.save();
                
                // Create blob and download
                const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                const url = URL.createObjectURL(blob);
                
                // Create download link
                const link = document.createElement('a');
                link.href = url;
                link.download = 'filled_birth_certificate.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // Clean up
                URL.revokeObjectURL(url);
                
                showStatus('PDF generated and downloaded successfully!', 'success');
                
            } catch (error) {
                console.error('Error generating PDF:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                setButtonLoading(false);
            }
        }

        // Form submission handler
        document.getElementById('birthForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Collect form data
            const formData = {
                name: document.getElementById('name').value.trim(),
                sex: document.getElementById('sex').value,
                dateOfBirth: document.getElementById('dateOfBirth').value,
                placeOfBirth: document.getElementById('placeOfBirth').value.trim(),
                fatherName: document.getElementById('fatherName').value.trim(),
                motherName: document.getElementById('motherName').value.trim(),
                address: document.getElementById('address').value.trim(),
                registrationNumber: document.getElementById('registrationNumber').value.trim()
            };
            
            // Validate required fields
            if (!formData.name || !formData.sex || !formData.dateOfBirth) {
                showStatus('Please fill in all required fields (Name, Sex, Date of Birth)', 'error');
                return;
            }
            
            // Generate PDF
            await generatePdf(formData);
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Birth Certificate Generator loaded');
            showStatus('Ready to generate birth certificates', 'info');
        });
    </script>
</body>
</html>
