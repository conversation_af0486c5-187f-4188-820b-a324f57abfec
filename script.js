// PDF field coordinates - adjust these based on your PDF template
// These coordinates are positioned to align with or appear below the labels as specified
const PDF_COORDINATES = {
    // Name - appears in line with "Name" label
    name: { x: 150, y: 200 },

    // Sex - appears in line with "Sex" label
    sex: { x: 400, y: 200 },

    // Date of Birth - appears just below "Date of Birth" label
    dateOfBirth: { x: 150, y: 240 },
    dateOfBirthWords: { x: 150, y: 260 },

    // Mother's Name - appears just below "Name of Mother" label
    motherName: { x: 150, y: 320 },

    // Father's Name - appears just below "Name of Father" label
    fatherName: { x: 150, y: 380 },

    // Address at Birth - appears just below the label
    addressAtBirth: { x: 150, y: 480 },

    // Permanent Address - appears just below the label
    permanentAddress: { x: 150, y: 560 },

    // Registration Number - appears in corresponding position
    registrationNumber: { x: 400, y: 650 },

    // Automatic date fields
    dateOfRegistration: { x: 150, y: 700 },
    dateOfIssue: { x: 400, y: 700 },
    updatedOn: { x: 150, y: 750 }
};

// AcroForm field name mappings for fallback
const ACROFORM_MAPPINGS = {
    name: ['NAME', 'Name', 'FullName', 'FULL_NAME', 'name', 'ChildName'],
    sex: ['SEX', 'Sex', 'Gender', 'GENDER', 'sex'],
    dateOfBirth: ['DOB', 'DateOfBirth', 'DATE_OF_BIRTH', 'BirthDate', 'dateOfBirth'],
    motherName: ['MOTHER', 'MotherName', 'MOTHER_NAME', 'Mother', 'motherName', 'NameOfMother'],
    fatherName: ['FATHER', 'FatherName', 'FATHER_NAME', 'Father', 'fatherName', 'NameOfFather'],
    addressAtBirth: ['ADDRESS_BIRTH', 'AddressAtBirth', 'BIRTH_ADDRESS', 'addressAtBirth'],
    permanentAddress: ['PERM_ADDRESS', 'PermanentAddress', 'PERMANENT_ADDRESS', 'permanentAddress'],
    registrationNumber: ['REG_NO', 'RegistrationNumber', 'REGISTRATION_NUMBER', 'RegNo', 'registrationNumber'],
    dateOfRegistration: ['REG_DATE', 'DateOfRegistration', 'REGISTRATION_DATE', 'dateOfRegistration'],
    dateOfIssue: ['ISSUE_DATE', 'DateOfIssue', 'DATE_OF_ISSUE', 'dateOfIssue'],
    updatedOn: ['UPDATED_ON', 'UpdatedOn', 'LAST_UPDATED', 'updatedOn']
};

// Utility functions
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 5000);
    }
}

function setButtonLoading(loading) {
    const btn = document.getElementById('generateBtn');
    btn.disabled = loading;
    if (loading) {
        btn.classList.add('loading');
        btn.textContent = '';
    } else {
        btn.classList.remove('loading');
        btn.textContent = 'Generate PDF Certificate';
    }
}

// Date formatting functions
function formatDate(date, format = 'DD-MM-YYYY') {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    
    if (format === 'DD-MM-YYYY') {
        return `${day}-${month}-${year}`;
    }
    return `${day}-${month}-${year}`;
}

function formatDateTime(date) {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}

// Convert number to words
function numberToWords(num) {
    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    
    if (num === 0) return 'Zero';
    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? ' ' + ones[num % 10] : '');
    if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 !== 0 ? ' ' + numberToWords(num % 100) : '');
    if (num < 100000) return numberToWords(Math.floor(num / 1000)) + ' Thousand' + (num % 1000 !== 0 ? ' ' + numberToWords(num % 1000) : '');
    
    return num.toString(); // Fallback for very large numbers
}

// Convert date to words
function dateToWords(dateString) {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.getMonth();
    const year = date.getFullYear();
    
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const dayWords = numberToWords(day);
    const monthWord = months[month];
    const yearWords = numberToWords(year);
    
    return `${dayWords} ${monthWord} ${yearWords}`;
}

// PDF loading function
async function loadPdfTemplate() {
    const possiblePaths = [
        'PDF/BirthTemplate.pdf',
        'PDF/Birth Template.pdf',
        './PDF/BirthTemplate.pdf',
        './PDF/Birth Template.pdf',
        'PDF/Birth%20Template.pdf',
        './PDF/Birth%20Template.pdf'
    ];
    
    let lastError = null;
    
    for (const path of possiblePaths) {
        try {
            console.log(`Trying to load PDF from: ${path}`);
            const response = await fetch(path);
            console.log(`Response for ${path}:`, response.status, response.statusText);
            
            if (response.ok) {
                const arrayBuffer = await response.arrayBuffer();
                console.log(`Successfully loaded PDF from: ${path} (${arrayBuffer.byteLength} bytes)`);
                return arrayBuffer;
            }
            lastError = `HTTP ${response.status}: ${response.statusText}`;
        } catch (error) {
            console.log(`Error trying path ${path}:`, error);
            lastError = error.message;
            continue;
        }
    }
    
    throw new Error(`Could not load PDF template. Last error: ${lastError}

Troubleshooting steps:
1. Ensure you're opening this via a web server (http://localhost) not file:// protocol
2. Check that "Birth Template.pdf" exists in the PDF folder
3. Try renaming the PDF file to remove spaces (e.g., "BirthTemplate.pdf")
4. Check browser console for detailed error messages

Current page URL: ${window.location.href}`);
}

// Try to fill PDF using AcroForm fields
async function fillPdfWithAcroForms(pdfDoc, formData) {
    try {
        const form = pdfDoc.getForm();
        const fields = form.getFields();

        // Log all available field names for debugging
        console.log('Available PDF form fields:', fields.map(field => field.getName()));

        let fieldsFound = 0;
        const currentDate = new Date();
        const currentDateFormatted = formatDate(currentDate);
        const currentDateTimeFormatted = formatDateTime(currentDate);

        // Prepare data with automatic fields
        const allData = {
            ...formData,
            dateOfBirth: formatDate(formData.dateOfBirth),
            dateOfBirthWords: dateToWords(formData.dateOfBirth),
            dateOfRegistration: currentDateFormatted,
            dateOfIssue: currentDateFormatted,
            updatedOn: currentDateTimeFormatted
        };

        // Try to fill each field using possible field name variations
        Object.keys(ACROFORM_MAPPINGS).forEach(formKey => {
            const value = allData[formKey];
            if (value) {
                const possibleNames = ACROFORM_MAPPINGS[formKey];

                for (const fieldName of possibleNames) {
                    try {
                        const field = form.getTextField(fieldName);
                        if (field) {
                            field.setText(String(value));
                            console.log(`Successfully filled AcroForm field "${fieldName}" with "${value}"`);
                            fieldsFound++;
                            break;
                        }
                    } catch (error) {
                        // Field doesn't exist or isn't a text field, try next name
                        continue;
                    }
                }
            }
        });

        console.log(`Successfully filled ${fieldsFound} AcroForm fields`);

        if (fieldsFound > 0) {
            // Flatten the form to make fields non-editable
            form.flatten();
            return true;
        }

        return false;
    } catch (error) {
        console.log('No AcroForm fields found or error accessing them:', error);
        return false;
    }
}

// Fill PDF with coordinate-based text placement
async function fillPdfWithCoordinates(pdfDoc, formData) {
    const pages = pdfDoc.getPages();
    const firstPage = pages[0];
    const { height } = firstPage.getSize();

    console.log('Filling PDF with coordinate-based text placement');

    // Helper function to add text
    function addText(text, coords, fontSize = 12, color = PDFLib.rgb(0, 0, 0)) {
        if (text && coords) {
            firstPage.drawText(String(text), {
                x: coords.x,
                y: height - coords.y,
                size: fontSize,
                color: color
            });
            console.log(`Added text "${text}" at coordinates (${coords.x}, ${coords.y})`);
        }
    }

    // Helper function to add multi-line text
    function addMultiLineText(text, coords, fontSize = 12, lineHeight = 20) {
        if (text && coords) {
            const lines = text.split('\n');
            lines.forEach((line, index) => {
                addText(line, { x: coords.x, y: coords.y + (index * lineHeight) }, fontSize);
            });
        }
    }
    
    // Current date for automatic fields
    const currentDate = new Date();
    const currentDateFormatted = formatDate(currentDate);
    const currentDateTimeFormatted = formatDateTime(currentDate);

    // Fill all fields according to specifications

    // 1. Name - appears in line with "Name" label
    addText(formData.name, PDF_COORDINATES.name, 12);

    // 2. Sex - appears in line with "Sex" label
    addText(formData.sex, PDF_COORDINATES.sex, 12);

    // 3. Date of Birth - appears just below "Date of Birth" label
    const dobFormatted = formatDate(formData.dateOfBirth);
    addText(dobFormatted, PDF_COORDINATES.dateOfBirth, 12);

    // Date of Birth in words - on the next line below numeric format
    const dobInWords = dateToWords(formData.dateOfBirth);
    addText(dobInWords, PDF_COORDINATES.dateOfBirthWords, 11);

    // 5. Mother's Name - appears just below "Name of Mother" label
    addText(formData.motherName, PDF_COORDINATES.motherName, 12);

    // 6. Father's Name - appears just below "Name of Father" label
    addText(formData.fatherName, PDF_COORDINATES.fatherName, 12);

    // 8. Address at Birth - appears just below the label
    addMultiLineText(formData.addressAtBirth, PDF_COORDINATES.addressAtBirth, 11, 15);

    // 9. Permanent Address - appears just below the label
    addMultiLineText(formData.permanentAddress, PDF_COORDINATES.permanentAddress, 11, 15);

    // 10. Registration Number - appears in corresponding position
    addText(formData.registrationNumber, PDF_COORDINATES.registrationNumber, 12);

    // 12. Date of Registration - automatically filled with current date
    addText(currentDateFormatted, PDF_COORDINATES.dateOfRegistration, 12);

    // 13. Date of Issue - automatically filled with current date
    addText(currentDateFormatted, PDF_COORDINATES.dateOfIssue, 12);

    // 14. Updated On - automatically filled with current date and time
    addText(currentDateTimeFormatted, PDF_COORDINATES.updatedOn, 10);
}

// Main PDF generation function
async function generatePdf(formData) {
    try {
        setButtonLoading(true);
        showStatus('Loading PDF template...', 'info');
        
        // Load the PDF template
        const existingPdfBytes = await loadPdfTemplate();
        
        showStatus('Processing PDF...', 'info');
        
        // Load PDF with pdf-lib
        const pdfDoc = await PDFLib.PDFDocument.load(existingPdfBytes);

        // Try to fill using AcroForm fields first
        showStatus('Attempting to fill PDF form fields...', 'info');
        const useAcroForms = await fillPdfWithAcroForms(pdfDoc, formData);

        // If no AcroForm fields were found or filled, use coordinate-based approach
        if (!useAcroForms) {
            showStatus('Using coordinate-based text placement...', 'info');
            await fillPdfWithCoordinates(pdfDoc, formData);
        } else {
            showStatus('Successfully filled PDF using form fields...', 'info');
        }
        
        // Generate the PDF
        showStatus('Generating final PDF...', 'info');
        const pdfBytes = await pdfDoc.save();
        
        // Create blob and download
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = 'filled_birth_certificate.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        URL.revokeObjectURL(url);
        
        showStatus('PDF generated and downloaded successfully!', 'success');
        
    } catch (error) {
        console.error('Error generating PDF:', error);
        showStatus(`Error: ${error.message}`, 'error');
    } finally {
        setButtonLoading(false);
    }
}

// Form validation
function validateForm(formData) {
    const requiredFields = ['name', 'sex', 'dateOfBirth', 'motherName', 'fatherName', 'addressAtBirth', 'permanentAddress', 'registrationNumber'];
    
    for (const field of requiredFields) {
        if (!formData[field] || formData[field].trim() === '') {
            return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
        }
    }
    
    // Validate date
    const birthDate = new Date(formData.dateOfBirth);
    const today = new Date();
    if (birthDate > today) {
        return 'Date of birth cannot be in the future.';
    }
    
    return null; // No validation errors
}

// Form submission handler
document.getElementById('birthForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Collect form data
    const formData = {
        name: document.getElementById('name').value.trim(),
        sex: document.getElementById('sex').value,
        dateOfBirth: document.getElementById('dateOfBirth').value,
        motherName: document.getElementById('motherName').value.trim(),
        fatherName: document.getElementById('fatherName').value.trim(),
        addressAtBirth: document.getElementById('addressAtBirth').value.trim(),
        permanentAddress: document.getElementById('permanentAddress').value.trim(),
        registrationNumber: document.getElementById('registrationNumber').value.trim()
    };
    
    // Validate form
    const validationError = validateForm(formData);
    if (validationError) {
        showStatus(validationError, 'error');
        return;
    }
    
    // Generate PDF
    await generatePdf(formData);
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('Birth Certificate Generator loaded');
    showStatus('Ready to generate birth certificates. Please fill out the form.', 'info');
    
    // Set max date for date of birth to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('dateOfBirth').setAttribute('max', today);
});
