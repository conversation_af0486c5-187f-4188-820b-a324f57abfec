// PDF field coordinates - corrected based on "After print data.pdf" analysis
// Y coordinates are measured from TOP of page (0 = top, higher values = lower on page)
const PDF_COORDINATES = {
    // Registration Number - top right field
    registrationNumber: { x: 55, y: 1155 },

    // Name - after "नाम / NAME:" label (row 1, left side)
    name: { x: 145, y: 670 },

    // Sex - after "लिंग / SEX:" label (row 1, right side)
    sex: { x: 670, y: 670 },

    // Date of Birth - after "जन्म तिथि / DATE OF BIRTH:" label (row 2, left side)
    dateOfBirth: { x: 55, y: 785 },

    // Date of Birth in words - below numeric date
    dateOfBirthWords: { x: 55, y: 800 },

    // Place of Birth - after "जन्म स्थान / PLACE OF BIRTH:" (row 2, right side)
    placeOfBirth: { x: 598, y: 820 },

    // Mother's Name - after "माता का नाम / NAME OF MOTHER:" label (row 3, left side)
    motherName: { x: 55, y: 900 },

    // Father's Name - after "पिता का नाम / NAME OF FATHER:" label (row 3, right side)
    fatherName: { x: 598, y: 900 },

    // Mother's Aadhaar - below mother's name
    motherAadhaar: { x: 55, y: 965 },

    // Father's Aadhaar - below father's name
    fatherAadhaar: { x: 598, y: 965 },

    // Address at Birth - after "ADDRESS OF PARENTS AT THE TIME OF BIRTH OF THE CHILD:" label
    addressAtBirth: { x: 55, y: 1053 },

    // Permanent Address - after "PERMANENT ADDRESS OF PARENTS:" label (positioned to the right)
    permanentAddress: { x: 320, y: 1053 },

    // Bottom section dates
    dateOfRegistration: { x: 598, y: 1155 },
    dateOfIssue: { x: 55, y: 1275 },
    updatedOn: { x: 152, y: 1362 }
};

// AcroForm field name mappings for fallback
const ACROFORM_MAPPINGS = {
    name: ['NAME', 'Name', 'FullName', 'FULL_NAME', 'name', 'ChildName'],
    sex: ['SEX', 'Sex', 'Gender', 'GENDER', 'sex'],
    dateOfBirth: ['DOB', 'DateOfBirth', 'DATE_OF_BIRTH', 'BirthDate', 'dateOfBirth'],
    placeOfBirth: ['POB', 'PlaceOfBirth', 'PLACE_OF_BIRTH', 'BirthPlace', 'placeOfBirth'],
    motherName: ['MOTHER', 'MotherName', 'MOTHER_NAME', 'Mother', 'motherName', 'NameOfMother'],
    fatherName: ['FATHER', 'FatherName', 'FATHER_NAME', 'Father', 'fatherName', 'NameOfFather'],
    motherAadhaar: ['MOTHER_AADHAAR', 'MotherAadhaar', 'AADHAAR_MOTHER', 'motherAadhaar'],
    fatherAadhaar: ['FATHER_AADHAAR', 'FatherAadhaar', 'AADHAAR_FATHER', 'fatherAadhaar'],
    addressAtBirth: ['ADDRESS_BIRTH', 'AddressAtBirth', 'BIRTH_ADDRESS', 'addressAtBirth'],
    permanentAddress: ['PERM_ADDRESS', 'PermanentAddress', 'PERMANENT_ADDRESS', 'permanentAddress'],
    registrationNumber: ['REG_NO', 'RegistrationNumber', 'REGISTRATION_NUMBER', 'RegNo', 'registrationNumber'],
    dateOfRegistration: ['REG_DATE', 'DateOfRegistration', 'REGISTRATION_DATE', 'dateOfRegistration'],
    dateOfIssue: ['ISSUE_DATE', 'DateOfIssue', 'DATE_OF_ISSUE', 'dateOfIssue'],
    updatedOn: ['UPDATED_ON', 'UpdatedOn', 'LAST_UPDATED', 'updatedOn']
};

// Utility functions
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 5000);
    }
}

function setButtonLoading(loading) {
    const btn = document.getElementById('generateBtn');
    btn.disabled = loading;
    if (loading) {
        btn.classList.add('loading');
        btn.textContent = '';
    } else {
        btn.classList.remove('loading');
        btn.textContent = 'Generate PDF Certificate';
    }
}

// Date formatting functions
function formatDate(date, format = 'DD-MM-YYYY') {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    
    if (format === 'DD-MM-YYYY') {
        return `${day}-${month}-${year}`;
    }
    return `${day}-${month}-${year}`;
}

function formatDateTime(date) {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
}

// Convert number to words
function numberToWords(num) {
    const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
    
    if (num === 0) return 'Zero';
    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? ' ' + ones[num % 10] : '');
    if (num < 1000) return ones[Math.floor(num / 100)] + ' Hundred' + (num % 100 !== 0 ? ' ' + numberToWords(num % 100) : '');
    if (num < 100000) return numberToWords(Math.floor(num / 1000)) + ' Thousand' + (num % 1000 !== 0 ? ' ' + numberToWords(num % 1000) : '');
    
    return num.toString(); // Fallback for very large numbers
}

// Convert date to words
function dateToWords(dateString) {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.getMonth();
    const year = date.getFullYear();
    
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const dayWords = numberToWords(day);
    const monthWord = months[month];
    const yearWords = numberToWords(year);
    
    return `${dayWords} ${monthWord} ${yearWords}`;
}

// PDF loading function
async function loadPdfTemplate() {
    const possiblePaths = [
        'PDF/BirthTemplate.pdf',
        'PDF/Birth Template.pdf',
        './PDF/BirthTemplate.pdf',
        './PDF/Birth Template.pdf',
        'PDF/Birth%20Template.pdf',
        './PDF/Birth%20Template.pdf'
    ];
    
    let lastError = null;
    
    for (const path of possiblePaths) {
        try {
            console.log(`Trying to load PDF from: ${path}`);
            const response = await fetch(path);
            console.log(`Response for ${path}:`, response.status, response.statusText);
            
            if (response.ok) {
                const arrayBuffer = await response.arrayBuffer();
                console.log(`Successfully loaded PDF from: ${path} (${arrayBuffer.byteLength} bytes)`);
                return arrayBuffer;
            }
            lastError = `HTTP ${response.status}: ${response.statusText}`;
        } catch (error) {
            console.log(`Error trying path ${path}:`, error);
            lastError = error.message;
            continue;
        }
    }
    
    throw new Error(`Could not load PDF template. Last error: ${lastError}

Troubleshooting steps:
1. Ensure you're opening this via a web server (http://localhost) not file:// protocol
2. Check that "Birth Template.pdf" exists in the PDF folder
3. Try renaming the PDF file to remove spaces (e.g., "BirthTemplate.pdf")
4. Check browser console for detailed error messages

Current page URL: ${window.location.href}`);
}

// Try to fill PDF using AcroForm fields
async function fillPdfWithAcroForms(pdfDoc, formData) {
    try {
        const form = pdfDoc.getForm();
        const fields = form.getFields();

        // Log all available field names for debugging
        console.log('Available PDF form fields:', fields.map(field => field.getName()));

        let fieldsFound = 0;
        const currentDate = new Date();
        const currentDateFormatted = formatDate(currentDate);
        const currentDateTimeFormatted = formatDateTime(currentDate);

        // Prepare data with automatic fields
        const allData = {
            ...formData,
            dateOfBirth: formatDate(formData.dateOfBirth),
            dateOfBirthWords: dateToWords(formData.dateOfBirth),
            dateOfRegistration: formData.dateOfRegistration ? formatDate(formData.dateOfRegistration) : currentDateFormatted,
            dateOfIssue: formData.dateOfIssue ? formatDate(formData.dateOfIssue) : currentDateFormatted,
            updatedOn: formData.updatedOn ? formatDateTime(formData.updatedOn) : currentDateTimeFormatted
        };

        // Try to fill each field using possible field name variations
        Object.keys(ACROFORM_MAPPINGS).forEach(formKey => {
            const value = allData[formKey];
            if (value) {
                const possibleNames = ACROFORM_MAPPINGS[formKey];

                for (const fieldName of possibleNames) {
                    try {
                        const field = form.getTextField(fieldName);
                        if (field) {
                            field.setText(String(value));
                            console.log(`Successfully filled AcroForm field "${fieldName}" with "${value}"`);
                            fieldsFound++;
                            break;
                        }
                    } catch (error) {
                        // Field doesn't exist or isn't a text field, try next name
                        continue;
                    }
                }
            }
        });

        console.log(`Successfully filled ${fieldsFound} AcroForm fields`);

        if (fieldsFound > 0) {
            // Flatten the form to make fields non-editable
            form.flatten();
            return true;
        }

        return false;
    } catch (error) {
        console.log('No AcroForm fields found or error accessing them:', error);
        return false;
    }
}

// Fill PDF with coordinate-based text placement
async function fillPdfWithCoordinates(pdfDoc, formData) {
    const pages = pdfDoc.getPages();
    const firstPage = pages[0];
    const { height } = firstPage.getSize();

    console.log('Filling PDF with coordinate-based text placement');

    // Load and embed custom fonts
    let calistoFont = null;
    let notoSansFont = null;

    try {
        // Try to load Calisto MT font
        const calistoFontBytes = await fetch('./Fonts/calisto-mt.ttf').then(res => res.arrayBuffer());
        calistoFont = await pdfDoc.embedFont(calistoFontBytes);
        console.log('Calisto MT font loaded successfully');
    } catch (error) {
        console.log('Could not load Calisto MT font:', error);
    }

    try {
        // Try to load Noto Sans Devanagari font
        const notoFontBytes = await fetch('./Fonts/NotoSansDevanagari-VariableFont_wdth,wght.ttf').then(res => res.arrayBuffer());
        notoSansFont = await pdfDoc.embedFont(notoFontBytes);
        console.log('Noto Sans Devanagari font loaded successfully');
    } catch (error) {
        console.log('Could not load Noto Sans Devanagari font:', error);
    }

    // Helper function to detect if text contains Devanagari characters
    function containsDevanagari(text) {
        if (!text) return false;
        // Devanagari Unicode range: U+0900–U+097F
        return /[\u0900-\u097F]/.test(text);
    }

    // Helper function to clean text for PDF compatibility
    function cleanTextForPdf(text) {
        if (!text) return '';
        // Convert to string and keep original characters for proper font rendering
        let cleanText = String(text);
        // Only replace problematic characters that aren't part of Devanagari
        cleanText = cleanText.replace(/[""]/g, '"'); // Smart quotes
        cleanText = cleanText.replace(/['']/g, "'"); // Smart apostrophes
        cleanText = cleanText.replace(/[–—]/g, "-"); // Em/en dashes
        return cleanText;
    }

    // Helper function to add text with proper font selection
    function addText(text, coords, fontSize = 14, color = PDFLib.rgb(0, 0, 0)) {
        if (text && coords) {
            try {
                const cleanText = cleanTextForPdf(text);
                if (cleanText) {
                    // Choose font based on text content
                    let font = null;
                    if (containsDevanagari(cleanText) && notoSansFont) {
                        font = notoSansFont;
                    } else if (calistoFont) {
                        font = calistoFont;
                    }

                    const textOptions = {
                        x: coords.x,
                        y: height - coords.y,
                        size: fontSize,
                        color: color
                    };

                    // Add font if available
                    if (font) {
                        textOptions.font = font;
                    }

                    firstPage.drawText(cleanText, textOptions);
                    console.log(`Added text "${cleanText}" at coordinates (${coords.x}, ${coords.y}) with font size ${fontSize}`);
                }
            } catch (error) {
                console.error(`Error adding text "${text}":`, error);
                // Try with a simple fallback without custom font
                try {
                    firstPage.drawText(String(text), {
                        x: coords.x,
                        y: height - coords.y,
                        size: fontSize,
                        color: color
                    });
                } catch (fallbackError) {
                    console.error('Fallback text also failed:', fallbackError);
                }
            }
        }
    }

    // Helper function to wrap text to fit within a specified width
    function wrapText(text, maxWidth, fontSize) {
        if (!text) return [];

        const words = String(text).split(' ');
        const lines = [];
        let currentLine = '';

        for (const word of words) {
            const testLine = currentLine ? `${currentLine} ${word}` : word;

            // Estimate text width (rough calculation)
            const estimatedWidth = testLine.length * (fontSize * 0.6);

            if (estimatedWidth <= maxWidth) {
                currentLine = testLine;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    // Word is too long, break it
                    lines.push(word);
                }
            }
        }

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    }

    // Helper function to add multi-line text with automatic wrapping
    function addMultiLineText(text, coords, fontSize = 14, lineHeight = 18, maxWidth = 250) {
        if (text && coords) {
            // First try to split by existing line breaks
            const paragraphs = String(text).split('\n');
            let currentY = coords.y;

            paragraphs.forEach(paragraph => {
                if (paragraph.trim()) {
                    // Wrap each paragraph to fit within maxWidth
                    const wrappedLines = wrapText(paragraph.trim(), maxWidth, fontSize);

                    wrappedLines.forEach(line => {
                        addText(line, { x: coords.x, y: currentY }, fontSize);
                        currentY += lineHeight;
                    });
                } else {
                    // Empty line, just add spacing
                    currentY += lineHeight;
                }
            });
        }
    }
    
    // Current date for automatic fields
    const currentDate = new Date();
    const currentDateFormatted = formatDate(currentDate);
    const currentDateTimeFormatted = formatDateTime(currentDate);

    // Fill all fields according to specifications and PDF layout

    // 1. Name - appears after "नाम / NAME:" label
    addText(formData.name, PDF_COORDINATES.name, 14, PDFLib.rgb(0, 0, 0));

    // 2. Sex - appears after "लिंग / SEX:" label
    addText(formData.sex, PDF_COORDINATES.sex, 14, PDFLib.rgb(0, 0, 0));

    // 3. Date of Birth - appears below "जन्म तिथि / DATE OF BIRTH:" label
    const dobFormatted = formatDate(formData.dateOfBirth);
    addText(dobFormatted, PDF_COORDINATES.dateOfBirth, 14, PDFLib.rgb(0, 0, 0));

    // Date of Birth in words - on the next line below numeric format
    const dobInWords = dateToWords(formData.dateOfBirth);
    addText(dobInWords, PDF_COORDINATES.dateOfBirthWords, 14, PDFLib.rgb(0, 0, 0));

    // 4. Place of Birth - appears after "जन्म स्थान / PLACE OF BIRTH:" (keep default if not provided)
    if (formData.placeOfBirth && formData.placeOfBirth.trim()) {
        addText(formData.placeOfBirth, PDF_COORDINATES.placeOfBirth, 14, PDFLib.rgb(0, 0, 0));
    }

    // 5. Mother's Name - appears below "माता का नाम / NAME OF MOTHER:" label
    addText(formData.motherName, PDF_COORDINATES.motherName, 14, PDFLib.rgb(0, 0, 0));

    // 6. Father's Name - appears below "पिता का नाम / NAME OF FATHER:" label
    addText(formData.fatherName, PDF_COORDINATES.fatherName, 14, PDFLib.rgb(0, 0, 0));

    // 7. Mother's Aadhaar - appears below "माता का आधार नंबर / AADHAAR NUMBER OF MOTHER:" (keep template if not provided)
    if (formData.motherAadhaar && formData.motherAadhaar.trim()) {
        addText(formData.motherAadhaar, PDF_COORDINATES.motherAadhaar, 14, PDFLib.rgb(0, 0, 0));
    }

    // Father's Aadhaar - appears below "पिता का आधार नंबर / AADHAAR NUMBER OF FATHER:" (keep template if not provided)
    if (formData.fatherAadhaar && formData.fatherAadhaar.trim()) {
        addText(formData.fatherAadhaar, PDF_COORDINATES.fatherAadhaar, 14, PDFLib.rgb(0, 0, 0));
    }

    // 8. Address at Birth - appears below the address label
    addMultiLineText(formData.addressAtBirth, PDF_COORDINATES.addressAtBirth, 12, 16, 200);

    // 9. Permanent Address - appears below permanent address label
    addMultiLineText(formData.permanentAddress, PDF_COORDINATES.permanentAddress, 12, 16, 200);

    // 10. Registration Number - appears in the number field
    addText(formData.registrationNumber, PDF_COORDINATES.registrationNumber, 14, PDFLib.rgb(0, 0, 0));

    // 12. Date of Registration - use form data if provided, otherwise current date
    const regDate = formData.dateOfRegistration ? formatDate(formData.dateOfRegistration) : currentDateFormatted;
    addText(regDate, PDF_COORDINATES.dateOfRegistration, 14, PDFLib.rgb(0, 0, 0));

    // 13. Date of Issue - use form data if provided, otherwise current date
    const issueDate = formData.dateOfIssue ? formatDate(formData.dateOfIssue) : currentDateFormatted;
    addText(issueDate, PDF_COORDINATES.dateOfIssue, 14, PDFLib.rgb(0, 0, 0));

    // 14. Updated On - use form data if provided, otherwise current date and time
    const updatedDate = formData.updatedOn ? formatDateTime(formData.updatedOn) : currentDateTimeFormatted;
    addText(updatedDate, PDF_COORDINATES.updatedOn, 14, PDFLib.rgb(0, 0, 0));
}

// Main PDF generation function
async function generatePdf(formData) {
    try {
        setButtonLoading(true);
        showStatus('Loading PDF template...', 'info');
        
        // Load the PDF template
        const existingPdfBytes = await loadPdfTemplate();
        
        showStatus('Processing PDF...', 'info');
        
        // Load PDF with pdf-lib
        const pdfDoc = await PDFLib.PDFDocument.load(existingPdfBytes);

        // Try to fill using AcroForm fields first
        showStatus('Attempting to fill PDF form fields...', 'info');
        const useAcroForms = await fillPdfWithAcroForms(pdfDoc, formData);

        // If no AcroForm fields were found or filled, use coordinate-based approach
        if (!useAcroForms) {
            showStatus('Using coordinate-based text placement...', 'info');
            await fillPdfWithCoordinates(pdfDoc, formData);
        } else {
            showStatus('Successfully filled PDF using form fields...', 'info');
        }
        
        // Generate the PDF
        showStatus('Generating final PDF...', 'info');
        const pdfBytes = await pdfDoc.save();
        
        // Create blob and download
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = 'filled_birth_certificate.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        URL.revokeObjectURL(url);
        
        showStatus('PDF generated and downloaded successfully!', 'success');
        
    } catch (error) {
        console.error('Error generating PDF:', error);
        showStatus(`Error: ${error.message}`, 'error');
    } finally {
        setButtonLoading(false);
    }
}

// Form validation
function validateForm(formData) {
    const requiredFields = ['name', 'sex', 'dateOfBirth', 'motherName', 'fatherName', 'addressAtBirth', 'permanentAddress', 'registrationNumber'];
    
    for (const field of requiredFields) {
        if (!formData[field] || formData[field].trim() === '') {
            return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
        }
    }
    
    // Validate date
    const birthDate = new Date(formData.dateOfBirth);
    const today = new Date();
    if (birthDate > today) {
        return 'Date of birth cannot be in the future.';
    }
    
    return null; // No validation errors
}

// Form submission handler
document.getElementById('birthForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Collect form data
    const formData = {
        name: document.getElementById('name').value.trim(),
        sex: document.getElementById('sex').value,
        dateOfBirth: document.getElementById('dateOfBirth').value,
        placeOfBirth: document.getElementById('placeOfBirth').value.trim(),
        motherName: document.getElementById('motherName').value.trim(),
        fatherName: document.getElementById('fatherName').value.trim(),
        motherAadhaar: document.getElementById('motherAadhaar').value.trim(),
        fatherAadhaar: document.getElementById('fatherAadhaar').value.trim(),
        addressAtBirth: document.getElementById('addressAtBirth').value.trim(),
        permanentAddress: document.getElementById('permanentAddress').value.trim(),
        registrationNumber: document.getElementById('registrationNumber').value.trim(),
        dateOfRegistration: document.getElementById('dateOfRegistration').value,
        dateOfIssue: document.getElementById('dateOfIssue').value,
        updatedOn: document.getElementById('updatedOn').value
    };
    
    // Validate form
    const validationError = validateForm(formData);
    if (validationError) {
        showStatus(validationError, 'error');
        return;
    }
    
    // Generate PDF
    await generatePdf(formData);
});

// Function to populate form with sample data
function populateFormWithSampleData() {
    const today = new Date().toISOString().split('T')[0];

    document.getElementById('name').value = 'ADITYA SHARMA';
    document.getElementById('sex').value = 'MALE';
    document.getElementById('dateOfBirth').value = '2021-07-15';
    document.getElementById('placeOfBirth').value = 'SWAR KHURD (SUAR), MILAK , RAMPUR, UTTAR PRADESH, 244924 / स्वर खुर्द (सुअर) , मिलक, रामपुर, उत्तर प्रदेश, 244924';
    document.getElementById('motherName').value = 'SHOBHA SHARMA';
    document.getElementById('fatherName').value = 'PANKAJ KUMAR';
    document.getElementById('motherAadhaar').value = 'XXXX-XXXX-5835';
    document.getElementById('fatherAadhaar').value = 'XXXX-XXXX-1978';
    document.getElementById('addressAtBirth').value = 'GRAM AHIMALAPUR POST TAJPUR SUB DIST KAIMGANJ DIST FARRUKHABAD UTTAR PRADESH 209651,';
    document.getElementById('permanentAddress').value = 'GRAM AHIMALAPUR POST TAJPUR SUB DIST KAIMGANJ DIST FARRUKHABAD UTTAR PRADESH 209651,';
    document.getElementById('registrationNumber').value = 'B202527334750000983';
    document.getElementById('dateOfRegistration').value = today;
    document.getElementById('dateOfIssue').value = today;
    document.getElementById('updatedOn').value = today;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('Birth Certificate Generator loaded');
    showStatus('Ready to generate birth certificates. Please fill out the form.', 'info');

    // Set max date for date of birth to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('dateOfBirth').setAttribute('max', today);

    // Populate form with sample data
    populateFormWithSampleData();
    showStatus('Form populated with sample data. Click Generate PDF to test.', 'success');
});
